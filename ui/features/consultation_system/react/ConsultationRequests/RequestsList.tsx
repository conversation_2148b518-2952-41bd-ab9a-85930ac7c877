import React from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Badge } from '@instructure/ui-badge'
import { IconUserLine, IconCalendarMonthLine, IconEditLine } from '@instructure/ui-icons'
import type { ConsultationRequest } from '../types'

interface RequestsListProps {
  requests: ConsultationRequest[]
  userRole: 'student' | 'faculty'
  loading: boolean
  onRefresh: () => void
}

const RequestsList: React.FC<RequestsListProps> = ({
  requests,
  userRole,
  loading,
  onRefresh
}) => {
  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateTimeString
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning'
      case 'approved':
        return 'success'
      case 'declined':
        return 'danger'
      case 'completed':
        return 'brand'
      case 'cancelled':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  if (requests.length === 0) {
    return (
      <View as="div" textAlign="center" padding="x-large">
        <div className="empty-state">
          <div className="empty-icon">
            <IconUserLine size="large" />
          </div>
          <Heading level="h3" margin="0 0 small 0">
            No Consultation Requests
          </Heading>
          <Text>
            {userRole === 'student' 
              ? "You haven't submitted any consultation requests yet. Click 'New Request' to get started."
              : "No consultation requests match your current filters. Try adjusting your search criteria."
            }
          </Text>
          {userRole === 'student' && (
            <View as="div" margin="medium 0 0 0">
              <Button
                color="primary"
                href="/consultation_requests/student_form"
              >
                Submit Your First Request
              </Button>
            </View>
          )}
        </div>
      </View>
    )
  }

  return (
    <View as="div" margin="medium 0 0 0">
      {requests.map(request => (
        <View
          key={request.id}
          as="div"
          background="primary"
          padding="medium"
          borderRadius="medium"
          borderWidth="small"
          borderColor="brand"
          margin="0 0 medium 0"
        >
          <View as="div" display="flex" justifyItems="space-between" alignItems="start">
            <View as="div" width="75%">
              <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                <IconUserLine size="small" />
                <Heading level="h4" margin="0 0 0 x-small">
                  {userRole === 'student' ? request.faculty_name : request.student_name}
                </Heading>
                <Text size="small" color="secondary" margin="0 0 0 small">
                  {userRole === 'faculty' && `(${request.student_id})`}
                </Text>
              </View>

              <View as="div" margin="0 0 small 0">
                <View as="div" display="flex" alignItems="center" margin="0 0 x-small 0">
                  <IconCalendarMonthLine size="x-small" />
                  <Text weight="bold" margin="0 0 0 x-small">
                    {formatDateTime(request.preferred_datetime)}
                  </Text>
                </View>
              </View>

              <View as="div" margin="0 0 small 0">
                <Text size="small" weight="bold" color="secondary">
                  {request.concern_type_display} • {request.status_display}
                </Text>
              </View>

              <View as="div" margin="small 0 0 0" background="secondary" padding="small" borderRadius="small">
                <View as="div" margin="0 0 x-small 0">
                  <Text size="small" weight="bold">
                    {userRole === 'student' ? 'Your Concern:' : 'Student\'s Concern:'}
                  </Text>
                </View>
                <Text size="small">
                  {request.description}
                </Text>
              </View>

              {request.faculty_comment && (
                <View as="div" margin="small 0 0 0" background="success" padding="small" borderRadius="small">
                  <View as="div" margin="0 0 x-small 0">
                    <Text size="small" weight="bold">
                      Faculty Comment:
                    </Text>
                  </View>
                  <Text size="small">
                    {request.faculty_comment}
                  </Text>
                </View>
              )}

              <View as="div" margin="small 0 0 0">
                <Text size="x-small" color="secondary">
                  Submitted: {new Date(request.created_at).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit'
                  })}
                  {request.updated_at !== request.created_at && (
                    <span>
                      {' • Updated: '}
                      {new Date(request.updated_at).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric',
                        hour: 'numeric',
                        minute: '2-digit'
                      })}
                    </span>
                  )}
                </Text>
              </View>
            </View>

            <View as="div">
              {userRole === 'faculty' && request.status === 'pending' && (
                <View as="div" margin="0 0 small 0">
                  <Button
                    size="small"
                    color="success"
                    href={`/consultation_requests/faculty_dashboard?highlight=${request.id}`}
                  >
                    Review
                  </Button>
                </View>
              )}
              {userRole === 'student' && request.status === 'pending' && (
                <View as="div" margin="0 0 small 0">
                  <Button
                    size="small"
                    renderIcon={() => <IconEditLine />}
                    href={`/consultation_requests/${request.id}/edit`}
                  >
                    Edit
                  </Button>
                </View>
              )}
              {request.status === 'approved' && (
                <View as="div" margin="0 0 small 0">
                  <Button
                    size="small"
                    href={`/calendar?event_id=${request.id}`}
                    target="_blank"
                  >
                    View in Calendar
                  </Button>
                </View>
              )}
            </View>
          </View>
        </View>
      ))}
    </View>
  )
}

export default RequestsList
