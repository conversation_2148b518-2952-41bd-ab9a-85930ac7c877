import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { TextInput } from '@instructure/ui-text-input'
import { Select } from '@instructure/ui-select'
import CanvasSelect from '@canvas/instui-bindings/react/Select'
import { TextArea } from '@instructure/ui-text-area'
import { FormFieldGroup } from '@instructure/ui-form-field'
import { Text } from '@instructure/ui-text'
import { Checkbox } from '@instructure/ui-checkbox'
import { IconCalendarMonthLine } from '@instructure/ui-icons'
import type {
  StudentInfo,
  FacultyUser,
  ConsultationRequestFormData,
  AvailableDate,
  AvailableDateTime,
  FormErrors
} from '../types'
import { CONCERN_TYPES } from '../types'
import FacultyCalendarModal from './FacultyCalendarModal'
import { MIN_DESCRIPTION_LENGTH } from '../utils/constants'

interface ConsultationRequestFormProps {
  studentInfo: StudentInfo
  availableFaculty: FacultyUser[]
  availableDates: AvailableDate[]
  availableTimes: AvailableDateTime[]
  selectedFaculty: string
  selectedDate: string
  onFacultyChange: (facultyId: string) => void
  onDateChange: (date: string) => void
  onSubmit: (data: ConsultationRequestFormData) => Promise<void>
  loading: boolean
  concernTypes: string[]
}

const ConsultationRequestForm: React.FC<ConsultationRequestFormProps> = ({
  studentInfo,
  availableFaculty,
  availableDates,
  availableTimes,
  selectedFaculty,
  selectedDate,
  onFacultyChange,
  onDateChange,
  onSubmit,
  loading,
  concernTypes
}) => {
  const [formData, setFormData] = useState<ConsultationRequestFormData>({
    faculty_time_slot_id: '',
    preferred_datetime: '',
    description: '',
    nature_of_concern: '',
    custom_concern: '',
    college_campus_institute: studentInfo.college_campus_institute || '',
    department_program: studentInfo.department_program || studentInfo.department || '',
    semester: studentInfo.semester || '',
    academic_year: studentInfo.academic_year || '',
    place_of_consultation: '',
    intervention_given: '',
    referral_made: '',
    students_adviser_agreement: false,
    prepared_by_name: '',
    prepared_by_designation: '',
    noted_by_program_chair: '',
    noted_by_college_dean: '',
    conformance_signature: ''
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [facultySearchValue, setFacultySearchValue] = useState<string>('')
  const [isShowingFacultyOptions, setIsShowingFacultyOptions] = useState(false)
  const [isCalendarModalOpen, setIsCalendarModalOpen] = useState(false)
  const [selectedFacultyForCalendar, setSelectedFacultyForCalendar] = useState<FacultyUser | null>(null)

  // Use nature of concern options from props (passed from backend)
  const natureOfConcernOptions = concernTypes.length > 0 ? concernTypes : CONCERN_TYPES

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!selectedFaculty) {
      newErrors.faculty = 'Please select a faculty member'
    }

    if (!formData.preferred_datetime) {
      newErrors.preferred_datetime = 'Please select a date and time'
    }

    if (!formData.nature_of_concern) {
      newErrors.nature_of_concern = 'Please select the nature of your concern'
    }

    if (formData.nature_of_concern === 'Others' && !formData.custom_concern?.trim()) {
      newErrors.custom_concern = 'Please specify the nature of your concern'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Please provide a description of your concern'
    } else if (formData.description.trim().length < MIN_DESCRIPTION_LENGTH) {
      newErrors.description = 'Please provide a more detailed description (at least 20 characters)'
    }

    // Required fields validation
    if (!formData.semester || !formData.semester.trim()) {
      newErrors.semester = 'Semester is required'
    }

    if (!formData.academic_year || !formData.academic_year.trim()) {
      newErrors.academic_year = 'Year/Section is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isFormValid = (): boolean => {
    // Check all required fields without setting errors
    // Note: We don't need selectedDate since we're using preferred_datetime directly
    return !!(
      selectedFaculty &&
      formData.preferred_datetime &&
      formData.nature_of_concern &&
      formData.description.trim() &&
      formData.description.trim().length >= MIN_DESCRIPTION_LENGTH &&
      (formData.nature_of_concern !== 'Others' || formData.custom_concern?.trim()) &&
      formData.semester?.trim() &&
      formData.academic_year?.trim()
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
      // Reset form on success
      setFormData({
        faculty_time_slot_id: '',
        preferred_datetime: '',
        description: '',
        nature_of_concern: '',
        custom_concern: '',
        college_campus_institute: '',
        department_program: '',
        semester: '',
        academic_year: '',
        place_of_consultation: '',
        intervention_given: '',
        referral_made: '',
        students_adviser_agreement: false,
        prepared_by_name: '',
        prepared_by_designation: '',
        noted_by_program_chair: '',
        noted_by_college_dean: '',
        conformance_signature: ''
      })
    } catch (error) {
      // Error handling is done in parent component
    }
  }

  const handleInputChange = (field: keyof ConsultationRequestFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'students_adviser_agreement' ? (value === 'true' || value === true) : value
    }))

    // Clear custom concern when nature of concern changes to something other than "Others"
    if (field === 'nature_of_concern' && value !== 'Others') {
      setFormData(prev => ({
        ...prev,
        custom_concern: ''
      }))
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }



  // Faculty search handlers
  const handleFacultyInputChange = (_event: React.ChangeEvent<HTMLInputElement>, value: string) => {
    setFacultySearchValue(value)
  }

  const handleShowFacultyOptions = () => {
    setIsShowingFacultyOptions(true)
  }

  const handleHideFacultyOptions = () => {
    setIsShowingFacultyOptions(false)
  }

  const handleSelectFaculty = (facultyId: string) => {
    const selectedFacultyMember = availableFaculty.find(f => f.id === facultyId)
    if (selectedFacultyMember) {
      setFacultySearchValue(selectedFacultyMember.name)
      setSelectedFacultyForCalendar(selectedFacultyMember)
      onFacultyChange(facultyId)
      // Open calendar modal after faculty selection
      setIsCalendarModalOpen(true)
    }
    setIsShowingFacultyOptions(false)
  }

  // Handle calendar modal close
  const handleCalendarModalClose = () => {
    setIsCalendarModalOpen(false)
  }

  // Handle time slot selection from calendar
  const handleTimeSlotSelect = (datetime: string, slotId?: string) => {
    setFormData(prev => ({
      ...prev,
      preferred_datetime: datetime,
      faculty_time_slot_id: slotId || ''
    }))

    // Clear any existing errors
    if (errors.preferred_datetime) {
      setErrors(prev => ({
        ...prev,
        preferred_datetime: ''
      }))
    }

    // Close the calendar modal
    setIsCalendarModalOpen(false)
  }

  // Handle opening calendar modal manually
  const handleOpenCalendar = () => {
    if (selectedFacultyForCalendar) {
      setIsCalendarModalOpen(true)
    }
  }

  // Filter faculty based on search input
  const filteredFaculty = availableFaculty.filter(faculty => {
    const searchValue = facultySearchValue || ''
    return faculty.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      (faculty.department && faculty.department.toLowerCase().includes(searchValue.toLowerCase()))
  })



  return (
    <View as="div" background="primary" padding="medium" borderRadius="medium">
      <Heading level="h2" margin="0 0 medium 0">
        Consultation Request Form
      </Heading>

      <form onSubmit={handleSubmit}>
        <FormFieldGroup description="Request Details" layout="stacked">
          <Select
            renderLabel="Select Faculty Member *"
            placeholder="Type to search or click to see all faculty members..."
            inputValue={facultySearchValue}
            isShowingOptions={isShowingFacultyOptions}
            onInputChange={handleFacultyInputChange}
            onRequestShowOptions={handleShowFacultyOptions}
            onRequestHideOptions={handleHideFacultyOptions}
            onRequestSelectOption={(_e, { id }) => handleSelectFaculty(id as string)}
            messages={errors.faculty ? [{ text: errors.faculty, type: 'error' }] : []}
            assistiveText="Type to search by faculty name or department, or click to see all options"
          >
            {filteredFaculty.map(faculty => (
              <Select.Option key={faculty.id} id={faculty.id} value={faculty.id}>
                <View as="div">
                  {faculty.name} {faculty.department && `(${faculty.department})`}
                  <View as="div">
                    <Text size="small" color="secondary">
                      {faculty.available_slots_count} available time slot{faculty.available_slots_count !== 1 ? 's' : ''}
                    </Text>
                  </View>
                </View>
              </Select.Option>
            ))}
          </Select>

          {/* Calendar Selection */}
          {selectedFaculty && (
            <View as="div">
              <View as="div" margin="0 0 small 0">
                <Text weight="bold" size="medium">
                  Select Date & Time *
                </Text>
              </View>
              <Flex gap="small" alignItems="center">
                <Flex.Item shouldGrow>
                  <TextInput
                    renderLabel=""
                    placeholder="Click 'View Calendar' to select date and time"
                    value={formData.preferred_datetime ?
                      new Date(formData.preferred_datetime).toLocaleString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric',
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                      }) : ''
                    }
                    onChange={() => {}} // Empty onChange handler since this is read-only
                    readOnly
                    messages={errors.preferred_datetime ? [{ text: errors.preferred_datetime, type: 'error' }] : []}
                  />
                </Flex.Item>
                <Flex.Item>
                  <Button
                    onClick={handleOpenCalendar}
                    renderIcon={<IconCalendarMonthLine />}
                    color="primary"
                  >
                    View Calendar
                  </Button>
                </Flex.Item>
              </Flex>
              {!formData.preferred_datetime && (
                <View as="div" margin="small 0 0 0">
                  <Text size="small" color="secondary">
                    Select a faculty member first, then use the calendar to choose your preferred consultation time.
                  </Text>
                </View>
              )}
            </View>
          )}

          <View as="div">
            <CanvasSelect
              id="nature-of-concern-select"
              label="Nature of Concern *"
              value={formData.nature_of_concern}
              onChange={(_e, value) => handleInputChange('nature_of_concern', value)}
            >
              <CanvasSelect.Option key="placeholder" id="placeholder" value="">
                Select the nature of your concern...
              </CanvasSelect.Option>
              {natureOfConcernOptions.map(type => (
                <CanvasSelect.Option key={type} id={type} value={type}>
                  {type}
                </CanvasSelect.Option>
              ))}
            </CanvasSelect>
            {errors.nature_of_concern && (
              <Text color="danger" size="small">
                {errors.nature_of_concern}
              </Text>
            )}
          </View>

          {formData.nature_of_concern === 'Others' && (
            <TextArea
              label="Please specify your concern"
              placeholder="Please provide details about your specific concern..."
              value={formData.custom_concern || ''}
              onChange={(e) => handleInputChange('custom_concern', e.target.value)}
              height="4rem"
              messages={errors.custom_concern ? [{ text: errors.custom_concern, type: 'error' }] : []}
            />
          )}

          <View as="div">
            <TextArea
              label="Description of Concern *"
              placeholder="Please provide a detailed description of what you would like to discuss during the consultation. This helps the faculty member prepare for your session."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              height="8rem"
              messages={errors.description ? [{ text: errors.description, type: 'error' }] : []}
            />
            <View as="div" margin="x-small 0 0 0" textAlign="end">
              <Text size="small" color={formData.description.trim().length >= 10 ? 'success' : 'secondary'}>
                {formData.description.trim().length}/10 characters minimum
                {formData.description.trim().length < 10 &&
                  ` (${10 - formData.description.trim().length} more needed)`
                }
              </Text>
            </View>
          </View>

          <View as="div" background="secondary" padding="small" borderRadius="small">
            <Text size="small" color="secondary">
              <strong>Note:</strong> Please provide as much detail as possible about your concern.
              This information will help the faculty member prepare for your consultation and ensure
              you get the most out of your session.
            </Text>
          </View>
        </FormFieldGroup>

        <FormFieldGroup description="Additional Information" layout="stacked">
          <TextInput
            renderLabel="College/Campus/Institute"
            placeholder="Enter your college, campus, or institute"
            value={formData.college_campus_institute || ''}
            onChange={(e) => handleInputChange('college_campus_institute', e.target.value)}
          />

          <TextInput
            renderLabel="Department/Program"
            placeholder="Enter your department or program"
            value={formData.department_program || ''}
            onChange={(e) => handleInputChange('department_program', e.target.value)}
          />

          <Flex gap="medium">
            <Flex.Item shouldGrow>
              <TextInput
                renderLabel="Semester *"
                placeholder="e.g., 1st Semester"
                value={formData.semester || ''}
                onChange={(e) => handleInputChange('semester', e.target.value)}
                messages={errors.semester ? [{ text: errors.semester, type: 'error' }] : []}
              />
            </Flex.Item>

            <Flex.Item shouldGrow>
              <TextInput
                renderLabel="Year/Section *"
                placeholder="Year/Section"
                value={formData.academic_year || ''}
                onChange={(e) => handleInputChange('academic_year', e.target.value)}
                messages={errors.academic_year ? [{ text: errors.academic_year, type: 'error' }] : []}
              />
            </Flex.Item>
          </Flex>

          <TextInput
            renderLabel="Place of Consultation"
            placeholder="Where will the consultation take place?"
            value={formData.place_of_consultation || ''}
            onChange={(e) => handleInputChange('place_of_consultation', e.target.value)}
          />
        </FormFieldGroup>

        <FormFieldGroup description="Consultation Outcome (To be filled by faculty)" layout="stacked">
          <TextArea
            label="Intervention Given"
            placeholder="Interventions or recommendations provided during consultation (filled by faculty)"
            value={formData.intervention_given || ''}
            onChange={(e) => handleInputChange('intervention_given', e.target.value)}
            height="6rem"
            disabled={true}
          />

          <TextArea
            label="Referral Made"
            placeholder="Any referrals made to other services or professionals (filled by faculty)"
            value={formData.referral_made || ''}
            onChange={(e) => handleInputChange('referral_made', e.target.value)}
            height="4rem"
            disabled={true}
          />

          <Checkbox
            label="Student's Adviser Agreement"
            checked={formData.students_adviser_agreement || false}
            onChange={(e) => handleInputChange('students_adviser_agreement', e.target.checked.toString())}
            disabled={true}
          />

          <View as="div" background="secondary" padding="small" borderRadius="small">
            <Text size="small" color="secondary">
              <strong>Note:</strong> The fields above will be completed by the faculty member during or after your consultation session.
            </Text>
          </View>
        </FormFieldGroup>

        <View as="div" margin="large 0 0 0">
          <Flex justifyItems="end">
            <Flex.Item>
              <Button
                type="submit"
                color="primary"
                disabled={loading || !isFormValid()}
              >
                {loading ? 'Submitting Request...' : 'Submit Consultation Request'}
              </Button>
            </Flex.Item>
          </Flex>
        </View>
      </form>

      {/* Faculty Calendar Modal */}
      <FacultyCalendarModal
        isOpen={isCalendarModalOpen}
        onClose={handleCalendarModalClose}
        faculty={selectedFacultyForCalendar}
        onTimeSlotSelect={handleTimeSlotSelect}
        selectedDateTime={formData.preferred_datetime}
      />
    </View>
  )
}

export default ConsultationRequestForm
